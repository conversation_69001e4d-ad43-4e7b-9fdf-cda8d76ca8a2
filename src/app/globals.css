@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 98%; /* Off-white #FAFAFA */
    --foreground: 240 10% 3.9%; /* Dark Grayish Blue */

    --card: 60 56% 91%; /* Warm Beige #F5F5DC - For flashcard background */
    --card-foreground: 60 25% 25%; /* Darker Beige/Brown text on flashcard */

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 56 33% 58%; /* Muted Gold #BDB76B - For buttons */
    --primary-foreground: 0 0% 98%; /* Off-white text on Muted Gold */

    --secondary: 60 30% 94%; /* Lighter beige for secondary elements */
    --secondary-foreground: 60 25% 25%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%; /* For muted text */

    --accent: 56 33% 65%; /* Lighter Muted Gold for hover/accent states */
    --accent-foreground: 60 25% 20%; /* Darker text for contrast on Lighter Muted Gold */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 85%; /* Slightly darker gray border */
    --input: 0 0% 92%; /* Lighter gray for input backgrounds */
    --ring: 56 33% 58%; /* Muted Gold for focus rings */
    
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 240 10% 10%; 
    --foreground: 0 0% 98%; 

    --card: 240 10% 15%; /* Darker card background for dark mode */
    --card-foreground: 0 0% 95%; /* Lighter text for dark mode cards */

    --popover: 240 10% 10%;
    --popover-foreground: 0 0% 98%;

    --primary: 56 33% 58%; /* Muted Gold remains primary */
    --primary-foreground: 0 0% 100%;

    --secondary: 240 10% 18%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 10% 18%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 60 56% 85%; /* Lighter Warm Beige for accent in dark mode */
    --accent-foreground: 60 25% 15%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 10% 20%;
    --input: 240 10% 20%;
    --ring: 56 33% 58%; /* Muted Gold for focus rings */
    
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html, body {
    @apply bg-background text-foreground overflow-x-hidden;
  }

  /* iOS Safe Area Support */
  :root {
    /* Define safe area inset custom properties with fallbacks */
    --safe-area-inset-top: env(safe-area-inset-top, 0px);
    --safe-area-inset-right: env(safe-area-inset-right, 0px);
    --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
    --safe-area-inset-left: env(safe-area-inset-left, 0px);
  }
}

/* Custom animation for flashcard flip */
.flashcard-flip-container {
  perspective: 1000px;
}

.flashcard-flipper {
  transition: transform 0.6s;
  transform-style: preserve-3d;
  position: relative;
}

.flashcard-front,
.flashcard-back {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.flashcard-front {
  z-index: 2;
  /* transform: rotateY(0deg); // Not needed as it's the default */
}

.flashcard-back {
  transform: rotateY(180deg);
}

.flashcard-flipper.flipped {
  transform: rotateY(180deg);
}

/* Line clamp utilities for responsive text truncation */
@layer utilities {
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Enhanced text wrapping for flashcard questions */
@layer components {
  .flashcard-question-text {
    word-break: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.4;
  }

  /* Mobile-specific adjustments */
  @media (max-width: 640px) {
    .flashcard-question-text {
      font-size: 0.875rem; /* 14px */
      line-height: 1.3;
    }

    /* Improve mobile flashcard layout */
    .flashcard-flip-container {
      min-height: 18rem; /* 288px - ensure minimum height on mobile */
      max-height: 20rem; /* 320px - prevent cards from being too tall */
    }

    /* Better mobile button spacing */
    .flashcard-mobile-buttons {
      padding: 0.5rem;
      gap: 0.75rem;
    }
  }

  /* Tablet-specific adjustments */
  @media (min-width: 641px) and (max-width: 1024px) {
    .flashcard-question-text {
      font-size: 0.9rem; /* 14.4px */
      line-height: 1.35;
    }
  }
}

/* Flashcard sidebar height fixes for iPad and tablet devices */
@layer components {
  .flashcard-sidebar {
    /* Ensure sidebar has proper height constraints */
    max-height: calc(100vh - 8rem);
    display: flex;
    flex-direction: column;
  }

  .flashcard-sidebar-content {
    /* Make the scrollable content area flex to fill available space */
    flex: 1;
    min-height: 0; /* Important for flex child to shrink */
    overflow: hidden;
  }

  /* iPad-specific fixes */
  @media (min-width: 768px) and (max-width: 1024px) {
    .flashcard-sidebar {
      max-height: calc(100vh - 6rem);
    }

    .flashcard-sidebar-content {
      /* Ensure proper scrolling on iPad */
      -webkit-overflow-scrolling: touch;
    }
  }

  /* Large tablet and desktop */
  @media (min-width: 1025px) {
    .flashcard-sidebar {
      max-height: calc(100vh - 4rem);
    }
  }
}

/* Mobile flashcard alignment fixes */
@layer components {
  /* Ensure proper mobile alignment for flashcard containers */
  @media (max-width: 640px) {
    .flashcard-container {
      padding-left: 1rem;
      padding-right: 1rem;
      margin-left: auto;
      margin-right: auto;
    }

    /* Fix button alignment on mobile */
    .flashcard-buttons {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 0.75rem;
      padding: 0 0.5rem;
    }

    /* Ensure cards don't overflow on small screens */
    .flashcard-flip-container {
      width: 100%;
      max-width: 100%;
      margin: 0 auto;
    }
  }
}

/* iOS PWA keyboard fix - prevent unwanted zoom while allowing keyboard */
@layer base {
  /* Prevent zoom on input focus for better UX while still allowing keyboard */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="search"],
  input[type="tel"],
  input[type="url"],
  textarea,
  select {
    font-size: 16px; /* iOS won't zoom if font-size is 16px or larger */
  }

  /* iOS PWA specific fixes */
  @supports (-webkit-touch-callout: none) {
    /* iOS-specific styles */
    input,
    textarea {
      /* Ensure inputs are properly styled for iOS */
      -webkit-appearance: none;
      border-radius: 0;
    }

    /* Fix for iOS PWA standalone mode input focus */
    input:focus,
    textarea:focus {
      /* Ensure focus works properly in standalone mode */
      outline: none;
      transform: translateZ(0); /* Force hardware acceleration */
    }
  }
}

/* Critical fix for iOS PWA keyboard issue - override select-none for inputs */
@layer utilities {
  /* Fix for iOS PWA standalone mode - allow text selection on inputs */
  @media all and (display-mode: standalone), (display-mode: fullscreen) {
    /* Override any select-none classes that might interfere with iOS keyboard */
    input,
    textarea,
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="search"],
    input[type="tel"],
    input[type="url"],
    input[type="number"] {
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
      user-select: text !important;
      /* Additional iOS PWA fixes */
      -webkit-touch-callout: default !important;
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    }

    /* Ensure input containers also allow selection */
    .relative:has(input),
    .relative:has(textarea) {
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
      user-select: text !important;
    }

    /* iOS PWA specific body fixes */
    body {
      /* Prevent iOS from adding default touch behaviors that interfere with inputs */
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -webkit-tap-highlight-color: transparent;
    }

    /* But allow text selection specifically for input elements and their labels */
    input, textarea, label, [contenteditable="true"] {
      -webkit-user-select: text !important;
      -webkit-touch-callout: default !important;
    }
  }
}

/* PWA-Specific Safe Area Handling */
@layer utilities {
  /* Apply safe area padding only in PWA standalone mode */
  @media all and (display-mode: standalone), (display-mode: fullscreen) {
    /* PWA-specific safe area classes that only apply in standalone mode */
    .pwa-safe-top {
      padding-top: var(--safe-area-inset-top);
    }

    .pwa-safe-bottom {
      padding-bottom: var(--safe-area-inset-bottom);
    }

    .pwa-safe-x {
      padding-left: calc(var(--safe-area-inset-left) + 16px);
      padding-right: calc(var(--safe-area-inset-right) + 16px);
    }

    .pwa-safe-y {
      padding-top: var(--safe-area-inset-top);
      padding-bottom: var(--safe-area-inset-bottom);
    }

    .pwa-safe-all {
      padding-top: var(--safe-area-inset-top);
      padding-right: calc(var(--safe-area-inset-right) + 16px);
      padding-bottom: var(--safe-area-inset-bottom);
      padding-left: calc(var(--safe-area-inset-left) + 16px);
    }

    /* Main content wrapper for PWA */
    .pwa-main {
      min-height: 100vh;
      min-height: 100dvh; /* Use dynamic viewport height when available */
      padding-top: var(--safe-area-inset-top);
      padding-left: calc(var(--safe-area-inset-left) + 16px);
      padding-right: calc(var(--safe-area-inset-right) + 16px);
      padding-bottom: var(--safe-area-inset-bottom);
    }

    /* Header areas that need top safe area */
    .pwa-header {
      padding-top: var(--safe-area-inset-top);
      padding-left: calc(var(--safe-area-inset-left) + 16px);
      padding-right: calc(var(--safe-area-inset-right) + 16px);
    }

    /* Footer areas that need bottom safe area */
    .pwa-footer {
      padding-bottom: var(--safe-area-inset-bottom);
      padding-left: calc(var(--safe-area-inset-left) + 16px);
      padding-right: calc(var(--safe-area-inset-right) + 16px);
    }
  }
}

/* iOS Safe Area Utility Classes */
@layer utilities {
  /* Safe area padding utilities */
  .safe-area-top {
    padding-top: var(--safe-area-inset-top);
  }

  .safe-area-right {
    padding-right: var(--safe-area-inset-right);
  }

  .safe-area-bottom {
    padding-bottom: var(--safe-area-inset-bottom);
  }

  .safe-area-left {
    padding-left: var(--safe-area-inset-left);
  }

  .safe-area-x {
    padding-left: calc(var(--safe-area-inset-left) + 16px);
    padding-right: calc(var(--safe-area-inset-right) + 16px);
  }

  .safe-area-y {
    padding-top: var(--safe-area-inset-top);
    padding-bottom: var(--safe-area-inset-bottom);
  }

  .safe-area-all {
    padding-top: var(--safe-area-inset-top);
    padding-right: calc(var(--safe-area-inset-right) + 16px);
    padding-bottom: var(--safe-area-inset-bottom);
    padding-left: calc(var(--safe-area-inset-left) + 16px);
  }

  /* Safe area margin utilities */
  .safe-margin-top {
    margin-top: var(--safe-area-inset-top);
  }

  .safe-margin-right {
    margin-right: var(--safe-area-inset-right);
  }

  .safe-margin-bottom {
    margin-bottom: var(--safe-area-inset-bottom);
  }

  .safe-margin-left {
    margin-left: var(--safe-area-inset-left);
  }

  .safe-margin-x {
    margin-left: calc(var(--safe-area-inset-left) + 16px);
    margin-right: calc(var(--safe-area-inset-right) + 16px);
  }

  .safe-margin-y {
    margin-top: var(--safe-area-inset-top);
    margin-bottom: var(--safe-area-inset-bottom);
  }

  .safe-margin-all {
    margin-top: var(--safe-area-inset-top);
    margin-right: calc(var(--safe-area-inset-right) + 16px);
    margin-bottom: var(--safe-area-inset-bottom);
    margin-left: calc(var(--safe-area-inset-left) + 16px);
  }
}

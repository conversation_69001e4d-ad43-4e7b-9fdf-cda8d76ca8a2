// This is a server component that handles the params
import { FlashcardSetClient } from '@/components/FlashcardSetClient';

interface FlashcardSetParams {
  params: Promise<{
    id: string;
  }>;
}

// Server component extracts the ID and passes it to the client component
export default async function FlashcardSetPage({ params }: FlashcardSetParams) {
  const { id } = await params;
  return <FlashcardSetClient id={id} />;
}
"use client";

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Flashcard } from './Flashcard';
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Smartphone, Menu, X, ThumbsUp, ThumbsDown } from 'lucide-react';
import type { FlashcardData } from '@/types';
import { cn } from '@/lib/utils';
import { VirtualizedFlashcardList } from './VirtualizedFlashcardList';
import { PerformanceMonitor, useRenderPerformance, useMemoryMonitor } from './PerformanceMonitor';
import {
  getPerformanceTier,
  optimizeFlashcardData,
  PerformanceTracker,
  getPerformanceRecommendations
} from '@/lib/performance-utils';

interface FlashcardViewerProps {
  flashcards: FlashcardData[];
}

export function FlashcardViewer({ flashcards }: FlashcardViewerProps) {
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [knownCards, setKnownCards] = useState<Set<number>>(new Set());
  const [activeFlashcards, setActiveFlashcards] = useState<FlashcardData[]>([]);
  const [showKnownCards, setShowKnownCards] = useState(false);
  const cardContainerRef = useRef<HTMLDivElement>(null);

  // Performance monitoring
  useRenderPerformance('FlashcardViewer', [flashcards.length, currentCardIndex, isFlipped, sidebarOpen]);
  useMemoryMonitor('FlashcardViewer', flashcards.length);

  // Performance optimization: memoize optimized flashcards
  const optimizedFlashcards = useMemo(() => optimizeFlashcardData(flashcards), [flashcards]);

  // Performance tracking
  const performanceTracker = useMemo(() => PerformanceTracker.getInstance(), []);

  // Log performance recommendations for large sets
  useEffect(() => {
    const tier = getPerformanceTier(flashcards.length);
    if (tier === 'LARGE_SET' || tier === 'VERY_LARGE_SET') {
      const recommendations = getPerformanceRecommendations(flashcards.length);
      console.log('[Performance] Recommendations for large flashcard set:', recommendations);
    }
  }, [flashcards.length]);

  // Initialize active flashcards from all flashcards
  useEffect(() => {
    const endTiming = performanceTracker.startTiming('flashcard-initialization');
    setActiveFlashcards(optimizedFlashcards);
    setKnownCards(new Set());
    setCurrentCardIndex(0);
    setIsFlipped(false);
    endTiming();
  }, [optimizedFlashcards, performanceTracker]);

  useEffect(() => {
    // Detect mobile device - improved logic for iPad handling
    const checkMobile = () => {
      const userAgent = navigator.userAgent || '';
      const screenWidth = window.innerWidth;

      // iPad detection - treat as tablet/desktop for sidebar purposes
      const isIpad = /ipad/i.test(userAgent) ||
                    (userAgent.includes('Mac') && 'ontouchend' in document);

      // True mobile devices (phones)
      const isMobileDevice = /android|webos|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());

      // Small screen detection
      const isSmallScreen = screenWidth < 640; // Use 640px as mobile breakpoint

      // iPad and tablets (768px+) should use desktop layout
      const shouldUseMobileLayout = (isMobileDevice && !isIpad) || isSmallScreen;

      setIsMobile(shouldUseMobileLayout);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Show sidebar by default on desktop
  useEffect(() => {
    if (!isMobile) {
      setSidebarOpen(true);
    } else {
      setSidebarOpen(false);
    }
  }, [isMobile]);

  if (!flashcards || flashcards.length === 0) {
    return null; 
  }

  const handleNextCard = useCallback(() => {
    setIsFlipped(false);
    // setTimeout to allow flip animation to reset before content change
    setTimeout(() => {
      setCurrentCardIndex((prevIndex) => (prevIndex + 1) % activeFlashcards.length);
    }, 50);
  }, [activeFlashcards.length]);

  const handlePreviousCard = useCallback(() => {
    setIsFlipped(false);
    setTimeout(() => {
      setCurrentCardIndex((prevIndex) =>
        prevIndex === 0 ? activeFlashcards.length - 1 : prevIndex - 1
      );
    }, 50);
  }, [activeFlashcards.length]);

  const handleSelectCard = useCallback((index: number) => {
    if (index === currentCardIndex) return;

    setIsFlipped(false);
    setTimeout(() => {
      setCurrentCardIndex(index);
      if (isMobile) {
        setSidebarOpen(false);
      }
    }, 50);
  }, [currentCardIndex, isMobile]);
  
  const handleMarkKnown = useCallback(() => {
    // Get the original index from the full flashcard array
    const currentCard = activeFlashcards[currentCardIndex];
    const currentCardId = currentCard.id !== undefined ? currentCard.id : currentCardIndex;

    // Add to known cards
    const newKnownCards = new Set(knownCards);
    newKnownCards.add(currentCardId);
    setKnownCards(newKnownCards);

    // Reset to non-flipped state (question side)
    setIsFlipped(false);

    // Move to the next card with a slight delay to allow animation
    setTimeout(() => {
      setCurrentCardIndex((prevIndex) => (prevIndex + 1) % activeFlashcards.length);
    }, 50);
  }, [activeFlashcards, currentCardIndex, knownCards]);

  const handleMarkNotKnown = useCallback(() => {
    // Reset to non-flipped state (question side)
    setIsFlipped(false);

    // Move to the next card with a slight delay to allow animation
    setTimeout(() => {
      setCurrentCardIndex((prevIndex) => (prevIndex + 1) % activeFlashcards.length);
    }, 50);
  }, [activeFlashcards.length]);
  
  const toggleShowKnownCards = useCallback(() => {
    const endTiming = performanceTracker.startTiming('toggle-known-cards');

    if (!showKnownCards) {
      // Showing all cards including known ones
      setShowKnownCards(true);
      setActiveFlashcards(optimizedFlashcards);
    } else {
      // Only showing cards not marked as known
      setShowKnownCards(false);
      const filteredCards = optimizedFlashcards.filter((_, index) => !knownCards.has(index));
      setActiveFlashcards(filteredCards);
      if (currentCardIndex >= filteredCards.length) {
        setCurrentCardIndex(0);
      }
    }
    setIsFlipped(false);
    endTiming();
  }, [showKnownCards, optimizedFlashcards, knownCards, currentCardIndex, performanceTracker]);



  // Memoize expensive calculations
  const currentFlashcard = useMemo(() =>
    activeFlashcards[currentCardIndex] || optimizedFlashcards[0],
    [activeFlashcards, currentCardIndex, optimizedFlashcards]
  );



  // Calculate learning progress
  const knownPercentage = useMemo(() =>
    optimizedFlashcards.length > 0
      ? Math.round((knownCards.size / optimizedFlashcards.length) * 100)
      : 0,
    [optimizedFlashcards.length, knownCards.size]
  );

  const noCardsLeft = useMemo(() =>
    activeFlashcards.length === 0 && !showKnownCards,
    [activeFlashcards.length, showKnownCards]
  );

  const toggleSidebar = useCallback(() => {
    setSidebarOpen(prevState => !prevState);
  }, []);

  return (
    <PerformanceMonitor componentName="FlashcardViewer" flashcardCount={flashcards.length}>
      <div className="w-full flex flex-col justify-center space-y-6 mx-auto">
      {/* Progress bar and controls */}
      <div className="flex flex-col sm:flex-row items-center justify-between gap-2 w-full mb-2">
        <div className="flex items-center gap-2">
          <Button 
            variant="ghost" 
            size="sm" 
            className="px-2" 
            onClick={toggleSidebar}
            aria-pressed={sidebarOpen}
            aria-label={sidebarOpen ? "Hide question index" : "Show question index"}
          >
            {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            <span className="ml-2">{sidebarOpen ? 'Hide Index' : 'Show Index'}</span>
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={toggleShowKnownCards}
            className="text-xs"
          >
            {showKnownCards ? "Hide Known Cards" : "Show All Cards"}
          </Button>
        </div>
        
        <div className="flex flex-col items-center gap-1">
          <div className="flex items-center gap-2">
            <div className="h-2 w-40 bg-muted rounded-full overflow-hidden">
              <div 
                className="h-full bg-primary" 
                style={{ width: `${knownPercentage}%` }}
              />
            </div>
            <span className="text-xs text-muted-foreground">{knownPercentage}% Known</span>
          </div>
          {isMobile && (
            <div className="flex items-center text-xs text-muted-foreground">
              <Smartphone className="h-3 w-3 mr-1" />
              {isFlipped ?
                <span>Swipe right for "Knew it", left for "Didn't know"</span> :
                <span>Tap to flip, use buttons to navigate</span>
              }
            </div>
          )}
        </div>
      </div>
      
      <div className="flex w-full gap-4 relative">
        {/* Questions Index Sidebar */}
        <div className={cn(
          "flashcard-sidebar transition-all duration-300 ease-in-out bg-card rounded-lg shadow-md",
          isMobile ? (
            sidebarOpen
              ? "w-full absolute inset-0 z-10"
              : "w-0 opacity-0 invisible overflow-hidden"
          ) : (
            sidebarOpen
              ? "w-1/3 lg:w-1/4 flex-shrink-0"
              : "w-0 opacity-0 invisible overflow-hidden"
          )
        )}>
          <div className="p-3 border-b flex items-center justify-between flex-shrink-0">
            <h3 className="font-medium text-sm">Questions Index</h3>
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-8 w-8"
              onClick={toggleSidebar}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </div>
          <div className="flashcard-sidebar-content h-80">
            <VirtualizedFlashcardList
              flashcards={optimizedFlashcards}
              currentCardIndex={currentCardIndex}
              knownCards={knownCards}
              onSelectCard={handleSelectCard}
              height={320} // 80 * 4 = 320px (h-80 in Tailwind)
            />
          </div>
        </div>

        {/* Main Flashcard Area */}
        <div 
          className={cn(
            "flex-1 transition-all duration-300",
            isMobile && sidebarOpen ? "opacity-0 invisible" : "opacity-100 visible"
          )}
        >
          {noCardsLeft ? (
            <div className="flex flex-col items-center justify-center p-8 bg-card rounded-lg shadow-xl h-80 text-center">
              <ThumbsUp className="h-12 w-12 text-primary mb-4" />
              <h3 className="text-xl font-medium mb-2">Great job!</h3>
              <p className="text-muted-foreground mb-6">You've learned all the flashcards in this set.</p>
              <Button onClick={toggleShowKnownCards}>
                Review All Cards Again
              </Button>
            </div>
          ) : (
            <>
              <div
                className="w-full"
                ref={cardContainerRef}
              >
                <Flashcard
                  question={currentFlashcard.question}
                  answer={currentFlashcard.answer}
                  isFlipped={isFlipped}
                  onFlip={() => setIsFlipped(!isFlipped)}
                  onMarkKnown={handleMarkKnown}
                  onMarkNotKnown={handleMarkNotKnown}
                />
              </div>

              {/* Knowledge rating buttons - only shown when card is flipped to answer */}
              {isFlipped && (
                <div className="flex justify-center w-full gap-4 mt-4">
                  <Button 
                    variant="outline" 
                    onClick={handleMarkNotKnown}
                    className="flex-1 py-6 border-red-200 bg-red-50 hover:bg-red-100 text-red-600"
                    size={isMobile ? "lg" : "default"}
                  >
                    <ThumbsDown className="h-5 w-5 mr-2" /> 
                    Didn't Know It
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    onClick={handleMarkKnown}
                    className="flex-1 py-6 border-green-200 bg-green-50 hover:bg-green-100 text-green-600"
                    size={isMobile ? "lg" : "default"}
                  >
                    <ThumbsUp className="h-5 w-5 mr-2" />
                    Knew It
                  </Button>
                </div>
              )}

              {/* Navigation controls - only shown when not showing the answer */}
              {!isFlipped && (
                <div className="flex flex-col sm:flex-row justify-between items-center w-full gap-4 mt-4">
                  <div className="flex justify-between w-full sm:w-auto gap-4">
                    <Button 
                      variant="outline" 
                      onClick={handlePreviousCard} 
                      disabled={activeFlashcards.length <= 1}
                      className={`${isMobile ? 'py-6 px-4 text-base' : ''}`}
                      size={isMobile ? "lg" : "default"}
                    >
                      <ChevronLeft className={`${isMobile ? 'h-5 w-5 mr-1' : 'h-4 w-4 mr-2'}`} /> 
                      {isMobile ? 'Prev' : 'Previous'}
                    </Button>
                    
                    <Button 
                      variant="outline" 
                      onClick={handleNextCard} 
                      disabled={activeFlashcards.length <= 1}
                      className={`${isMobile ? 'py-6 px-4 text-base' : ''}`}
                      size={isMobile ? "lg" : "default"}
                    >
                      {isMobile ? 'Next' : 'Next'} 
                      <ChevronRight className={`${isMobile ? 'h-5 w-5 ml-1' : 'h-4 w-4 ml-2'}`} />
                    </Button>
                  </div>
                  
                  <p className="text-sm text-muted-foreground mt-2 sm:mt-0">
                    Card {currentCardIndex + 1} of {activeFlashcards.length}
                    {knownCards.size > 0 && ` (${knownCards.size} learned)`}
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
    </PerformanceMonitor>
  );
}
